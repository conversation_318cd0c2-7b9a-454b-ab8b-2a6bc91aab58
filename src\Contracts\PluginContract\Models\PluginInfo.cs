using PluginContract.Enums;

namespace PluginContract.Models;

public sealed record PluginInfo(
    string Name,
    string Version,
    string Description,
    PluginType Type,
    string Author,
    bool IsEnabled = true
);

public sealed record PluginConfiguration(
    string PluginName,
    Dictionary<string, object> Settings
);

public abstract record NotificationRequest(
    string Message,
    Dictionary<string, object>? Metadata = null
);

public abstract record NotificationResponse(
    bool IsSuccess,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
);
