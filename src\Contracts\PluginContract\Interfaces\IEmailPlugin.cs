using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for email plugins
/// </summary>
public interface IEmailPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default);
}

public sealed record EmailRequest(
    string To,
    string Subject,
    string Body,
    string? From = null,
    List<string>? Cc = null,
    List<string>? Bcc = null,
    List<EmailAttachment>? Attachments = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Body, Metadata);

public sealed record EmailAttachment(
    string FileName,
    byte[] Content,
    string ContentType
);

public sealed record EmailResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
