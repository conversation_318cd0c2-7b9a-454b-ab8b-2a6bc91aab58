using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for push notification plugins
/// </summary>
public interface IPushNotificationPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendPushNotificationAsync(PushNotificationRequest request, CancellationToken cancellationToken = default);
}

public sealed record PushNotificationRequest(
    string DeviceToken,
    string Title,
    string Body,
    Dictionary<string, object>? Data = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Body, Metadata);

public sealed record PushNotificationResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
