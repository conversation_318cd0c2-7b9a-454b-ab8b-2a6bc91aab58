using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Specific interface for SMS plugins
/// </summary>
public interface ISmsPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default);
}

public sealed record SmsRequest(
    string PhoneNumber,
    string Message,
    string? From = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Message, Metadata);

public sealed record SmsResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
